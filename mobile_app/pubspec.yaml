name: mcq_quiz_app
description: MCQ Quiz System for Post Office Departmental Exam Preparation
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # Supabase Authentication
  supabase_flutter: ^2.8.0

  # Firebase - Keep Firestore for now (can migrate to Supabase DB later)
  firebase_core: ^2.15.1
  firebase_auth: ^4.20.0
  cloud_firestore: ^4.13.6

  # HTTP client for Firebase Auth REST API
  http: ^1.1.0
  crypto: ^3.0.3
  # firebase_storage: ^11.5.6  # Temporarily disabled due to Crashlytics dependency
  # firebase_messaging: ^14.7.10  # Temporarily disabled due to Crashlytics dependency
  # firebase_analytics: ^10.7.4  # Temporarily disabled due to Crashlytics dependency
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # UI Components
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # Navigation
  go_router: ^12.1.3
  
  # Utilities
  shared_preferences: ^2.2.2
  connectivity_plus: ^5.0.2
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # Authentication
  pinput: ^3.0.1
  country_code_picker: ^3.0.0
  # google_sign_in: ^6.1.6  # Temporarily disabled due to iOS dependency conflict
  local_auth: ^2.1.7

  # Security
  screen_protector: ^1.4.2

  # Notifications
  # flutter_local_notifications: ^15.1.0  # Temporarily disabled due to Android API compatibility
  
  # Charts and Analytics
  fl_chart: ^0.68.0
  syncfusion_flutter_charts: ^24.2.9
  
  # File Handling
  path_provider: ^2.1.1
  
  # Date and Time
  intl: ^0.18.1
  
  # HTTP
  dio: ^5.4.0
  
  # Local Database
  sqflite: ^2.3.0
  
  # Image Handling
  image_picker: ^1.0.4
  
  # Animations
  flutter_animate: ^4.3.0
  
  # Toast and Dialogs
  fluttertoast: ^8.2.4
  
  # Pull to Refresh
  pull_to_refresh: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/illustrations/
  
  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
