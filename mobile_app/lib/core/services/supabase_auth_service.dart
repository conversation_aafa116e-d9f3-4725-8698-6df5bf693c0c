import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../config/supabase_config.dart';

/// Supabase Authentication Service
/// Handles user authentication using Supabase Auth
class SupabaseAuthService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Register user with Supabase Auth
  static Future<Map<String, dynamic>> registerUser({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    if (kDebugMode) {
      print('DEBUG: 🔐 Starting Supabase user registration for: $email');
    }

    try {
      // Step 1: Register user with Supabase Auth
      if (kDebugMode) {
        print('DEBUG: 📝 Calling Supabase signUp...');
        print('DEBUG: 📝 Email: $email');
        print('DEBUG: 📝 Password length: ${password.length}');
      }

      final AuthResponse response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'name': name,
          'phone_number': phoneNumber,
          'office_name': officeName,
          'designation': designation,
          'user_type': 'mobile_user',
        },
      );

      if (kDebugMode) {
        print('DEBUG: ✅ Supabase signUp completed');
        print('DEBUG: 📝 User: ${response.user != null ? 'not null' : 'null'}');
        print(
            'DEBUG: 📝 Session: ${response.session != null ? 'not null' : 'null'}');
      }

      final User? user = response.user;
      if (user == null) {
        if (kDebugMode) {
          print('DEBUG: ❌ User registration returned null');
        }
        return {
          'success': false,
          'error': 'user_null',
          'message': 'Account creation failed. Please try again.',
        };
      }

      if (kDebugMode) {
        print('DEBUG: ✅ Supabase user created successfully: ${user.id}');
        print('DEBUG: 📝 User email: ${user.email}');
        print('DEBUG: 📝 Email confirmed: ${user.emailConfirmedAt != null}');
      }

      // Step 2: Store additional user data in Firestore (for now)
      // TODO: Migrate to Supabase database later
      bool firestoreSuccess = false;
      try {
        if (kDebugMode) {
          print('DEBUG: 📝 Storing user data in Firestore...');
        }

        await _storeUserDataInFirestore(
          uid: user.id,
          email: email,
          name: name,
          phoneNumber: phoneNumber,
          officeName: officeName,
          designation: designation,
        );

        firestoreSuccess = true;
        if (kDebugMode) {
          print('DEBUG: ✅ User data stored in Firestore successfully');
        }
      } catch (firestoreError) {
        if (kDebugMode) {
          print('DEBUG: ❌ Firestore storage failed: $firestoreError');
        }
        // Don't fail registration if Firestore fails
        firestoreSuccess = false;
      }

      return {
        'success': true,
        'uid': user.id,
        'email': user.email,
        'emailConfirmed': user.emailConfirmedAt != null,
        'firestoreSuccess': firestoreSuccess,
        'message': firestoreSuccess
            ? 'Account created successfully'
            : 'Account created (data sync pending)',
      };
    } on AuthException catch (authError) {
      if (kDebugMode) {
        print('DEBUG: ❌ Supabase Auth error: ${authError.message}');
        print('DEBUG: ❌ Error code: ${authError.statusCode}');
      }

      return {
        'success': false,
        'error': authError.statusCode ?? 'auth_error',
        'message': _getSupabaseErrorMessage(authError),
      };
    } catch (generalError) {
      if (kDebugMode) {
        print('DEBUG: ❌ General registration error: $generalError');
        print('DEBUG: ❌ Error type: ${generalError.runtimeType}');
      }

      return {
        'success': false,
        'error': 'unknown',
        'message': 'Registration failed: ${generalError.toString()}',
      };
    }
  }

  /// Sign in user with Supabase Auth
  static Future<Map<String, dynamic>> signInUser({
    required String email,
    required String password,
  }) async {
    if (kDebugMode) {
      print('DEBUG: 🔐 Starting Supabase user sign in for: $email');
    }

    try {
      final AuthResponse response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      final User? user = response.user;
      if (user == null) {
        return {
          'success': false,
          'error': 'user_null',
          'message': 'Sign in failed. Please try again.',
        };
      }

      if (kDebugMode) {
        print('DEBUG: ✅ User signed in successfully: ${user.id}');
        print('DEBUG: 📝 Email confirmed: ${user.emailConfirmedAt != null}');
      }

      return {
        'success': true,
        'uid': user.id,
        'email': user.email,
        'emailConfirmed': user.emailConfirmedAt != null,
        'message': 'Sign in successful',
      };
    } on AuthException catch (authError) {
      if (kDebugMode) {
        print('DEBUG: ❌ Supabase Auth sign in error: ${authError.message}');
      }

      return {
        'success': false,
        'error': authError.statusCode ?? 'auth_error',
        'message': _getSupabaseErrorMessage(authError),
      };
    } catch (generalError) {
      if (kDebugMode) {
        print('DEBUG: ❌ General sign in error: $generalError');
      }

      return {
        'success': false,
        'error': 'unknown',
        'message': 'Sign in failed: ${generalError.toString()}',
      };
    }
  }

  /// Send password reset email
  static Future<Map<String, dynamic>> sendPasswordReset(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(email);
      return {
        'success': true,
        'message': 'Password reset email sent',
      };
    } on AuthException catch (authError) {
      return {
        'success': false,
        'error': authError.statusCode ?? 'auth_error',
        'message': _getSupabaseErrorMessage(authError),
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'unknown',
        'message': 'Failed to send password reset email',
      };
    }
  }

  /// Store user data in Firestore (temporary - will migrate to Supabase DB)
  static Future<void> _storeUserDataInFirestore({
    required String uid,
    required String email,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    final userData = <String, dynamic>{
      'uid': uid,
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'officeName': officeName,
      'designation': designation,
      'userType': 'mobile_user',
      'emailVerified': false,
      'profileComplete': true,
      'isActive': true,
      'quizzesTaken': 0,
      'totalScore': 0,
      'averageScore': 0.0,
      'preferences': {
        'notifications': true,
        'darkMode': false,
        'language': 'en',
      },
      'createdAt': FieldValue.serverTimestamp(),
      'lastLoginAt': FieldValue.serverTimestamp(),
    };

    await _firestore
        .collection(SupabaseConfig.mobileUsersTable)
        .doc(uid)
        .set(userData);
  }

  /// Create user document from Supabase user data (for missing documents)
  static Future<void> createUserDocumentFromSupabaseUser({
    required String uid,
    required String email,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    if (kDebugMode) {
      print('DEBUG: 🔧 Creating user document from Supabase user data');
    }

    await _storeUserDataInFirestore(
      uid: uid,
      email: email,
      name: name,
      phoneNumber: phoneNumber,
      officeName: officeName,
      designation: designation,
    );
  }

  /// Get user data from Firestore (temporary - will migrate to Supabase DB)
  static Future<Map<String, dynamic>?> getUserData(String uid) async {
    try {
      if (kDebugMode) {
        print('DEBUG: 📖 Getting user data for UID: $uid');
      }

      final doc = await _firestore
          .collection(SupabaseConfig.mobileUsersTable)
          .doc(uid)
          .get();

      if (doc.exists) {
        if (kDebugMode) {
          print('DEBUG: ✅ User data found');
        }
        return doc.data();
      } else {
        if (kDebugMode) {
          print('DEBUG: ⚠️ User document not found');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to get user data: $e');
      }
      return null;
    }
  }

  /// Get Supabase error message
  static String _getSupabaseErrorMessage(AuthException e) {
    switch (e.statusCode) {
      case '400':
        if (e.message.contains('email')) {
          return 'Please enter a valid email address.';
        } else if (e.message.contains('password')) {
          return 'Password must be at least 6 characters long.';
        }
        return 'Invalid request. Please check your input.';
      case '422':
        if (e.message.contains('already registered')) {
          return 'An account already exists with this email address.';
        }
        return 'Email already registered or invalid format.';
      case '401':
        return 'Invalid email or password.';
      case '429':
        return 'Too many requests. Please try again later.';
      case '500':
        return 'Server error. Please try again later.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }

  /// Get current user
  static User? getCurrentUser() {
    return _supabase.auth.currentUser;
  }

  /// Sign out
  static Future<void> signOut() async {
    await _supabase.auth.signOut();
  }

  /// Check if current user's email is verified
  static Future<bool> isEmailVerified() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user != null) {
        // Refresh user data to get latest verification status
        await _supabase.auth.refreshSession();
        final refreshedUser = _supabase.auth.currentUser;
        return refreshedUser?.emailConfirmedAt != null;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to check email verification: $e');
      }
      return false;
    }
  }

  /// Resend email verification
  static Future<Map<String, dynamic>> resendEmailVerification() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user != null && user.emailConfirmedAt == null) {
        // Supabase doesn't have a direct resend verification method
        // We need to use the resend method with the user's email
        await _supabase.auth.resend(
          type: OtpType.signup,
          email: user.email!,
        );

        return {
          'success': true,
          'message': 'Verification email sent successfully',
        };
      } else {
        return {
          'success': false,
          'error': 'No user found or email already verified',
          'message': 'No user found or email already verified',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to resend email verification: $e');
      }
      return {
        'success': false,
        'error': 'resend_failed',
        'message': 'Failed to resend verification email: ${e.toString()}',
      };
    }
  }

  /// Email validation
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Password validation
  static String? validatePassword(String password) {
    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    return null;
  }

  /// Test Supabase connectivity
  static Future<Map<String, dynamic>> testSupabaseConnection() async {
    if (kDebugMode) {
      print('DEBUG: 🧪 Testing Supabase connectivity...');
    }

    try {
      // Test 1: Check if Supabase is initialized
      final currentUser = _supabase.auth.currentUser;
      if (kDebugMode) {
        print('DEBUG: 📝 Supabase initialized: true');
        print('DEBUG: 📝 Current user: ${currentUser?.id ?? 'none'}');
      }

      // Test 2: Try a simple auth operation (get session)
      final session = _supabase.auth.currentSession;
      if (kDebugMode) {
        print(
            'DEBUG: 📝 Current session: ${session != null ? 'active' : 'none'}');
      }

      return {
        'success': true,
        'message': 'Supabase connectivity test passed',
        'supabaseInitialized': true,
        'hasSession': session != null,
        'currentUser': currentUser?.id,
      };
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Supabase connectivity test failed: $e');
      }

      return {
        'success': false,
        'error': e.toString(),
        'message': 'Supabase connectivity test failed',
      };
    }
  }
}
