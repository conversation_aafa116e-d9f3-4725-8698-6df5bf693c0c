import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/supabase_auth_service.dart';

/// User model for email authentication
class EmailUser {
  final String uid;
  final String email;
  final String name;
  final String phoneNumber;
  final String officeName;
  final String designation;
  final bool emailVerified;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;

  const EmailUser({
    required this.uid,
    required this.email,
    required this.name,
    required this.phoneNumber,
    required this.officeName,
    required this.designation,
    required this.emailVerified,
    this.createdAt,
    this.lastLoginAt,
  });

  EmailUser copyWith({
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return EmailUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      officeName: officeName ?? this.officeName,
      designation: designation ?? this.designation,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'officeName': officeName,
      'designation': designation,
      'emailVerified': emailVerified,
      'createdAt': createdAt?.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  factory EmailUser.fromSupabaseUser(User user, Map<String, dynamic> userData) {
    return EmailUser(
      uid: user.id,
      email: user.email ?? userData['email'] ?? '',
      name: userData['name'] ?? user.userMetadata?['name'] ?? '',
      phoneNumber:
          userData['phoneNumber'] ?? user.userMetadata?['phone_number'] ?? '',
      officeName:
          userData['officeName'] ?? user.userMetadata?['office_name'] ?? '',
      designation:
          userData['designation'] ?? user.userMetadata?['designation'] ?? '',
      emailVerified: user.emailConfirmedAt != null,
      createdAt: _parseDateTime(userData['createdAt']) ??
          (user.createdAt is DateTime ? user.createdAt as DateTime : null),
      lastLoginAt: _parseDateTime(userData['lastLoginAt']) ??
          (user.lastSignInAt is DateTime
              ? user.lastSignInAt as DateTime
              : null),
    );
  }

  /// Helper method to parse DateTime from various formats
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    try {
      if (value is Timestamp) {
        // Firestore Timestamp
        return value.toDate();
      } else if (value is String) {
        // ISO string format
        return DateTime.parse(value);
      } else if (value is DateTime) {
        // Already a DateTime
        return value;
      } else {
        if (kDebugMode) {
          print('DEBUG: ⚠️ Unknown date format: ${value.runtimeType} - $value');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to parse date: $value - $e');
      }
      return null;
    }
  }
}

/// Email Authentication state
class EmailAuthState {
  final EmailUser? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;
  final bool isRegistering;
  final bool isLoggingIn;

  const EmailAuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
    this.isRegistering = false,
    this.isLoggingIn = false,
  });

  EmailAuthState copyWith({
    EmailUser? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
    bool? isRegistering,
    bool? isLoggingIn,
  }) {
    return EmailAuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isRegistering: isRegistering ?? this.isRegistering,
      isLoggingIn: isLoggingIn ?? this.isLoggingIn,
    );
  }
}

/// Email Authentication Provider
class EmailAuthNotifier extends StateNotifier<EmailAuthState> {
  EmailAuthNotifier() : super(const EmailAuthState()) {
    _checkAuthState();
  }

  /// Check current authentication state
  void _checkAuthState() {
    final user = Supabase.instance.client.auth.currentUser;
    if (user != null) {
      _loadUserData(user);
    }
  }

  /// Load user data from Firestore
  Future<void> _loadUserData(User supabaseUser) async {
    try {
      final userData = await SupabaseAuthService.getUserData(supabaseUser.id);
      if (userData != null) {
        final emailUser = EmailUser.fromSupabaseUser(supabaseUser, userData);

        // Check if email is verified before setting as authenticated
        if (supabaseUser.emailConfirmedAt != null) {
          state = state.copyWith(
            user: emailUser,
            isAuthenticated: true,
            error: null,
          );
        } else {
          // Email not verified, don't authenticate
          state = state.copyWith(
            user: null,
            isAuthenticated: false,
            error: 'Email verification required',
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to load user data: $e');
      }
    }
  }

  /// Register new user with email and password
  Future<bool> registerUser({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    state = state.copyWith(isRegistering: true, isLoading: true, error: null);

    try {
      if (kDebugMode) {
        print('DEBUG: 📧 Starting registration for: $email');
      }

      final result = await SupabaseAuthService.registerUser(
        email: email,
        password: password,
        name: name,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
      );

      if (result['success'] == true) {
        // Don't authenticate user immediately after registration
        // User must verify email first before being able to login
        state = state.copyWith(
          user: null, // Don't set user until email is verified
          isAuthenticated: false, // Keep as false until email verification
          isRegistering: false,
          isLoading: false,
          error: null,
        );

        if (kDebugMode) {
          print('DEBUG: ✅ Registration successful: ${result['user_id']}');
          print('DEBUG: 📧 Email verification required before login');
        }

        // Sign out the user immediately after registration
        // They must verify email and then login again
        await SupabaseAuthService.signOut();

        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Registration failed: $e');
      }
      state = state.copyWith(
        isRegistering: false,
        isLoading: false,
        error: e.toString(),
      );
    }

    return false;
  }

  /// Sign in user with email and password
  Future<bool> signInUser({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoggingIn: true, isLoading: true, error: null);

    try {
      if (kDebugMode) {
        print('DEBUG: 🔐 Starting login for: $email');
      }

      final result = await SupabaseAuthService.signInUser(
        email: email,
        password: password,
      );

      if (result['success'] == true) {
        if (kDebugMode) {
          print('DEBUG: 📝 Sign-in result received: ${result.keys}');
          print(
              'DEBUG: 📝 Email confirmed in result: ${result['emailConfirmed']}');
        }

        // Get the current user from Supabase client after successful sign-in
        final user = Supabase.instance.client.auth.currentUser;
        if (kDebugMode) {
          print(
              'DEBUG: 📝 Current user from Supabase: ${user != null ? 'not null' : 'null'}');
          if (user != null) {
            print('DEBUG: 📝 User ID: ${user.id}');
            print(
                'DEBUG: 📝 Email confirmed: ${user.emailConfirmedAt != null}');
          }
        }

        if (user != null) {
          // Check if email is verified
          if (user.emailConfirmedAt == null) {
            state = state.copyWith(
              isLoggingIn: false,
              isLoading: false,
              error:
                  'Please verify your email address before logging in. Check your inbox for the verification link.',
            );
            return false;
          }

          // Load user data from Firestore
          final userData = await SupabaseAuthService.getUserData(user.id);
          if (userData != null) {
            final emailUser = EmailUser.fromSupabaseUser(user, userData);
            state = state.copyWith(
              user: emailUser,
              isAuthenticated: true,
              isLoggingIn: false,
              isLoading: false,
              error: null,
            );

            if (kDebugMode) {
              print('DEBUG: ✅ Login successful: ${user.id}');
            }
            return true;
          } else {
            // User data not found in Firestore, but user is authenticated
            // Create a basic EmailUser from Supabase user data
            if (kDebugMode) {
              print(
                  'DEBUG: ⚠️ User data not found in Firestore, creating from Supabase user');
            }

            final emailUser = EmailUser.fromSupabaseUser(user, {});
            state = state.copyWith(
              user: emailUser,
              isAuthenticated: true,
              isLoggingIn: false,
              isLoading: false,
              error: null,
            );

            if (kDebugMode) {
              print(
                  'DEBUG: ✅ Login successful with basic user data: ${user.id}');
            }
            return true;
          }
        } else {
          // User is null despite successful sign-in
          if (kDebugMode) {
            print('DEBUG: ❌ User is null despite successful sign-in');
          }
          state = state.copyWith(
            isLoggingIn: false,
            isLoading: false,
            error: 'Login failed: User data not available',
          );
          return false;
        }
      } else {
        // Handle sign-in error
        if (kDebugMode) {
          print('DEBUG: ❌ Sign-in failed');
          print('DEBUG: 📝 Error code: ${result['error']}');
          print('DEBUG: 📝 Error message: ${result['message']}');
        }

        state = state.copyWith(
          isLoggingIn: false,
          isLoading: false,
          error: result['message'] ?? result['error'] ?? 'Login failed',
        );
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Login failed: $e');
      }
      state = state.copyWith(
        isLoggingIn: false,
        isLoading: false,
        error: e.toString(),
      );
    }

    return false;
  }

  /// Send password reset email
  Future<bool> sendPasswordReset(String email) async {
    try {
      final result = await SupabaseAuthService.sendPasswordReset(email);
      if (result['success'] == true) {
        return true;
      } else {
        state = state.copyWith(
            error: result['message'] ?? 'Failed to send password reset email');
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      await SupabaseAuthService.signOut();
      state = const EmailAuthState();
      if (kDebugMode) {
        print('DEBUG: ✅ User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Sign out failed: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Check if user is authenticated
  bool get isAuthenticated => state.isAuthenticated && state.user != null;

  /// Get current user
  EmailUser? get currentUser => state.user;
}

/// Email Auth Provider
final emailAuthProvider =
    StateNotifierProvider<EmailAuthNotifier, EmailAuthState>((ref) {
  return EmailAuthNotifier();
});
